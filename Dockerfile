#Build project
FROM harbor-in.greatld.com/kezhaozhao/qcc-benefishield-web:base-1.0.0 as build
WORKDIR /app
COPY package.json yarn.lock .npmrc .yarnrc ./
# 获取 yfiles
RUN git clone http://<EMAIL>:18888/confidential/yfiles-license.git \
    && mkdir -p ./libs/yfiles \
    && cp ./yfiles-license/yWorks.yFilesHTML.License.json ./libs/yfiles/license.json \
    && cp ./yfiles-license/yfiles-26.0.3.tgz ./libs/yfiles/yfiles-26.0.3.tgz \
    && rm -rf ./yfiles-license
RUN yarn install
COPY . /app/
RUN yarn build

FROM harbor-in.greatld.com/kezhaozhao/nginx:1.26.1-alpine3.19-slim
COPY --from=build /app/dist /app
CMD ["nginx","-g","daemon off;"]
