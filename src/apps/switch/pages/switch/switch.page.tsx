import { defineComponent, ref, computed, onMounted } from 'vue';
import { Icon, Modal } from 'ant-design-vue';
import { isArray } from 'lodash';
import { useRoute } from 'vue-router/composables';

import { Status } from '@/config';
import { useStore } from '@/store';
import { THEME_COLOR } from '@/shared/config/theme-color.config';
import env from '@/shared/config/env';
import { removeItem } from '@/utils/storage';
import { useWebTitle } from '@/shared/composables/use-web-title';
import { sleep } from '@/utils';

import SwitchOrganization from './widgets/switch-organization';
import styles from './switch.page.module.less';
import Logo from './widgets/logo/logo';

const userFilter = 'STAGING_USER_SELECTOR';
const dateFilter = 'STAGING_DATE_SELECTOR';

const SwitchPage = defineComponent({
  name: 'SwitchPage',

  setup() {
    useWebTitle('切换组织');

    const status = ref(Status.IDLE);
    const loading = ref(false);

    const store = useStore();

    const profile = computed(() => store.getters['user/profile']);
    const organizations = computed(() => {
      const originOrganizations = store.getters['user/organizations'];
      return (
        (isArray(originOrganizations) ? originOrganizations : [])
          // 仅显示包含 SAAS_UBO 套餐的组织
          // .filter((org) => org.organizationBundles?.some((bundle) => bundle?.serviceCode === 'SAAS_UBO'))
          .map((org, index) => {
            org.test = `background:${THEME_COLOR[index % 4].color}`;
            return org;
          })
      );
    });

    const getOrganizations = () => store.dispatch('user/getOrganizations');
    const switchOrganization = (organizationId: number) => store.dispatch('user/switchOrganization', organizationId);

    const route = useRoute();

    /**
     * 控制跳转
     * @param id
     */
    const handleRedirect = async (id?: string | number) => {
      const redirect = route.query.redirect ?? `${env.ENTERPRISE_HOME}/qcc/e/changeOrg/${id}?redirect=/e`;
      if (typeof redirect === 'string') {
        window.location.href = redirect;
      }
    };

    // 新版切换
    const handleSelect = async (organizationId) => {
      loading.value = true;
      await switchOrganization(organizationId);
      // 删除
      removeItem(userFilter);
      removeItem(dateFilter);

      await sleep(300);
      loading.value = false;

      // 切换后跳转
      handleRedirect(organizationId);
    };

    /**
     * 创建组织
     * NOTE: 不允许用户自行创建组织
     */
    const handleCreateOrg = () => {
      // 不让创建组织了
      Modal.info({
        title: '温馨提示',
        content: '如果您需要创建新组织请联系咨询顾问400-088-8275。',
        okText: '知道了',
      });
    };

    onMounted(() => {
      getOrganizations();
    });

    return {
      status,
      loading,
      organizations,

      handleCreateOrg,
      handleSelect,
      handleRedirect,

      profile,
      getOrganizations,
      switchOrganization,
    };
  },

  render() {
    // console.log(this.profile);
    return (
      <div class={styles.container}>
        <Logo class={styles.logo} />
        {this.status === Status.PENDING || !this.profile ? (
          <Icon type="loading" />
        ) : (
          <SwitchOrganization
            class={styles.organization}
            currentOrgId={this.profile.currentOrg}
            dataSource={this.organizations}
            loading={this.loading}
            onEnter={this.handleSelect}
            onClose={this.handleRedirect}
            onCreate={this.handleCreateOrg}
          />
        )}
        {/* <EnterpriseNameModal title="创建组织" onUpdate={handleSelect} ref="modalRef" /> */}
      </div>
    );
  },
});

export default SwitchPage;
