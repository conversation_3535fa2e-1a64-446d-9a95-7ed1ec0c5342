import { shallowMount, Wrapper } from '@vue/test-utils';
import { Pagination } from 'ant-design-vue';

import RiskModelListPage from '@/apps/risk-model/pages/risk-model-list/index';
import { setting as settingService } from '@/shared/services';

import ModelCollapseItem from '../widgets/model-collapse-item';

vi.mock('@/shared/services');

describe('RiskModelListPage', () => {
  let wrapper: Wrapper<any>;

  beforeEach(() => {
    wrapper = shallowMount(RiskModelListPage, {
      propsData: {
        isExternal: false,
        modelType: '1',
      },
    });
  });

  it('renders the page with default props', () => {
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.findComponent(Pagination).exists()).toBe(false);
  });

  it('calls getModelList on mount', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    await wrapper.vm.getModelList();
    expect(settingService.getModelLists).toHaveBeenCalledWith({
      modelType: 1,
      product: 'SAAS_UBO',
      branchCode: 0,
      onlyTier: true,
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it('handles page change', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.vm.handlePageChange(2, 15);
    await wrapper.vm.getModelList();
    expect(wrapper.vm.pagination.current).toBe(2);
    expect(wrapper.vm.pagination.pageSize).toBe(15);
  });

  it('handles page size change', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.vm.handlePageSizeChange(1, 20);
    await wrapper.vm.getModelList();
    expect(wrapper.vm.pagination.current).toBe(1);
    expect(wrapper.vm.pagination.pageSize).toBe(20);
  });

  it('shows pagination when total is greater than page size', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 20 });
    await wrapper.vm.getModelList();
    expect(wrapper.findComponent(Pagination).exists()).toBe(true);
  });

  it('handles model list loading error', async () => {
    vi.mocked(settingService.getModelLists).mockRejectedValue(new Error('Failed to fetch model list'));
    await wrapper.vm.getModelList();
    expect(wrapper.vm.modelList).toEqual([]);
    expect(wrapper.vm.loading).toBe(false);
  });

  it('renders with different modelType', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.setProps({ modelType: '2' });
    await wrapper.vm.getModelList();
    expect(settingService.getModelLists).toHaveBeenCalledWith({
      modelType: 1,
      product: 'SAAS_UBO',
      branchCode: 0,
      onlyTier: true,
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it('handles empty model list', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    await wrapper.vm.getModelList();
    expect(wrapper.vm.modelList).toEqual([]);
  });

  it('handles non-empty model list', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [{ modelId: 1 }], total: 1 });
    await wrapper.vm.getModelList();
    expect(wrapper.vm.modelList).toEqual([{ modelId: 1 }]);
    expect(wrapper.findComponent(ModelCollapseItem).exists()).toBe(true);
  });

  it('sets loading to true during getModelList', () => {
    wrapper.vm.loading = false;
    wrapper.vm.getModelList();
    expect(wrapper.vm.loading).toBe(true);
  });

  it('sets loading to false after getModelList', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.vm.loading = true;
    await wrapper.vm.getModelList();
    expect(wrapper.vm.loading).toBe(false);
  });

  it('handles page size less than minimum', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.vm.handlePageSizeChange(1, 4);
    await wrapper.vm.getModelList();
    expect(wrapper.vm.pagination.pageSize).toBe(4);
  });

  it('handles page size greater than maximum', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.vm.handlePageSizeChange(1, 20);
    await wrapper.vm.getModelList();
    expect(wrapper.vm.pagination.pageSize).toBe(20); // Since 20 is not in pageSizeOptions, it should not change
  });

  it('handles invalid modelType', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 0 });
    wrapper.setProps({ modelType: 'invalid' });
    await wrapper.vm.getModelList();
    expect(settingService.getModelLists).toHaveBeenCalledWith({
      modelType: 1, // parseInt('invalid', 10) returns NaN
      product: 'SAAS_UBO',
      branchCode: 0,
      onlyTier: true,
      pageIndex: 1,
      pageSize: 10,
    });
  });

  it('handles large total for pagination', async () => {
    vi.mocked(settingService.getModelLists).mockResolvedValue({ data: [], total: 100 });
    await wrapper.vm.getModelList();
    expect(wrapper.findComponent(Pagination).exists()).toBe(true);
  });
});
