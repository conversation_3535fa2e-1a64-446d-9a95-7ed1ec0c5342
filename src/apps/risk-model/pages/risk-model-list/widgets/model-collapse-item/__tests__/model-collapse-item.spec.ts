import { mount, shallowMount } from '@vue/test-utils';
import { Collapse, Spin } from 'ant-design-vue';
import { flush } from '@sentry/vue';

import { setting } from '@/shared/services';
import { flushPromises } from '@/test-utils/flush-promises';

import ModelCollapseItem from '..';
import ModelItem from '../../model-item';
import QIcon from '@/components/global/q-icon';

vi.mock('@/shared/services', () => ({
  setting: {
    getModelCopyLists: vi.fn(),
  },
}));

describe('ModelCollapseItem', () => {
  const mockModelData = {
    modelId: '123',
    modelName: 'Test Model',
    updateDate: '2023-10-01T12:00:00Z',
    branchCode: 'BRANCH_CODE',
  };

  it('renders correctly with initial state', () => {
    const wrapper = shallowMount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    expect(wrapper.findComponent(Spin).exists()).toBe(true);
    expect(wrapper.findComponent(Collapse).exists()).toBe(true);
    expect(wrapper.findComponent(QIcon).exists()).toBe(true);
  });

  it('fetches model copy list on mount if showList is false', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();

    const collapse = wrapper.findComponent(Collapse);
    collapse.vm.$emit('change');

    expect(setting.getModelCopyLists).toHaveBeenCalledWith({
      riskModelId: '123',
      product: 'SAAS_UBO',
      pageIndex: 1,
      pageSize: 100,
      branchCode: 'BRANCH_CODE',
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.vm.showList).toBe(true);
    expect(wrapper.vm.modelCopyList).toEqual([{ modelId: '456', modelName: 'Copy Model' }]);
  });

  it('handles page change correctly', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();

    wrapper.vm.handlePageChange(2, 50);
    await wrapper.vm.$nextTick();

    expect(setting.getModelCopyLists).toHaveBeenCalledWith({
      riskModelId: '123',
      product: 'SAAS_UBO',
      pageIndex: 2,
      pageSize: 50,
      branchCode: 'BRANCH_CODE',
    });
  });

  it('handles page size change correctly', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();

    wrapper.vm.handlePageSizeChange(2, 50);
    await wrapper.vm.$nextTick();

    expect(setting.getModelCopyLists).toHaveBeenCalledWith({
      riskModelId: '123',
      product: 'SAAS_UBO',
      pageIndex: 1,
      pageSize: 50,
      branchCode: 'BRANCH_CODE',
    });
  });

  it('renders correctly when modelCopyList is empty after fetch', async () => {
    const mockResponse = {
      data: [],
      total: 0,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(ModelItem).exists()).toBe(false);
  });

  it('handles onChange in ModelItem correctly', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();
    const collapse = wrapper.findComponent(Collapse);
    collapse.vm.$emit('change');
    await wrapper.vm.$nextTick();
    wrapper.findComponent(ModelItem).vm.$emit('change', { modelId: '456', modelName: 'Updated Model' });

    expect(setting.getModelCopyLists).toHaveBeenCalled();
  });

  it('renders correctly with isExternal true', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: true,
        modelType: 'testType',
      },
    });

    await wrapper.vm.$nextTick();
    const collapse = wrapper.findComponent(Collapse);
    collapse.vm.$emit('change');
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(ModelItem).props('isExternal')).toBe(true);
  });

  it('renders correctly with modelType prop', async () => {
    const mockResponse = {
      data: [{ modelId: '456', modelName: 'Copy Model' }],
      total: 1,
    };

    vi.mocked<any>(setting.getModelCopyLists).mockResolvedValue(mockResponse);

    const wrapper = mount(ModelCollapseItem, {
      propsData: {
        modelData: mockModelData,
        isExternal: false,
        modelType: 'specificType',
      },
    });

    await wrapper.vm.$nextTick();
    const collapse = wrapper.findComponent(Collapse);
    collapse.vm.$emit('change');
    await wrapper.vm.$nextTick();

    expect(wrapper.findComponent(ModelItem).props('modelType')).toBe('specificType');
  });
});
