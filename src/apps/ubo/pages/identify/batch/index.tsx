import qs from 'querystring';
import { defineComponent, ref, computed, provide, nextTick, onMounted } from 'vue';
import { Spin, message } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { cloneDeep } from 'lodash';

import { batchImport } from '@/shared/services';
import HeroicLayout from '@/shared/layouts/heroic';
import BatchUpload from '@/shared/components/batch-upload';
import { useAbility } from '@/libs/plugins/user-ability';
import { objectValuesToNumeric } from '@/utils/transform/object/object-values-to-numeric';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import GuideWidget from './widgets/guide';
import SearchResult from './widgets/search-result';
import styles from './batch.module.less';
import { TABLE_COLUMNS } from './config';

const IdentifyBatchPage = defineComponent({
  name: 'IdentifyBatchPage',
  setup() {
    const init = ref(true);
    const router = useRouter();
    const loading = ref(false);
    const dataSource = ref<any>([]);
    const track = useTrack();
    const getParams = computed(() => {
      return {
        batchType: 0,
        businessType: [BatchBusinessTypeEnum.Diligence_File, BatchBusinessTypeEnum.Diligence_ID, BatchBusinessTypeEnum.Diligence_Customer],
        pageSize: 10,
        pageIndex: 1,
      };
    });
    const hasData = computed(() => {
      return dataSource.value.length > 0;
    });

    // 更新单个任务状态
    const updateBatchItem = (incomingItem: Record<string, any>) => {
      const oriData = cloneDeep(dataSource.value);
      const changeIndex = oriData.findIndex((item) => +incomingItem.batchId === item.batchId);
      if (changeIndex > -1) {
        oriData[changeIndex] = {
          ...oriData[changeIndex],
          status: +incomingItem.status,
          canRetry: +incomingItem.canRetry,
          statisticsInfo: {
            ...oriData[changeIndex].statisticsInfo,
            ...objectValuesToNumeric(incomingItem.statisticsInfo),
          },
        };
        dataSource.value = oriData;
      }
    };

    const ability = useAbility();

    provide('needMessage', false);

    const fetchData = async () => {
      dataSource.value = [];
    };

    useRoomSocket('/rover/socket', {
      filter: (messageData) =>
        messageData.roomType === 'BatchProcessMonitor' &&
        [BatchBusinessTypeEnum.Diligence_File, BatchBusinessTypeEnum.Diligence_ID, BatchBusinessTypeEnum.Diligence_Customer].includes(
          +messageData.data.businessType
        ),
      update: updateBatchItem,
      refresh: fetchData,
    });

    const updateData = async (data) => {
      if (data?.toDetail && data?.batchId) {
        return router.push({
          name: 'upload-confirm',
          params: {
            type: 'batch-investigation',
          },
          query: {
            batchId: data.batchId,
          },
        });
      }
      // 选择上传后
      message.success('批量排查任务正在进行中！请稍后在批量排查任务中查看结果');
      return fetchData();
    };

    const gotoDetail = (record) => {
      track(createTrackEvent(6208, '批量排查', '排查详情'));
      router.push({
        path: `batch/${record.batchId}`,
        query: {
          settingId: record?.batchInfo?.settingId,
        },
      });
    };

    onMounted(() => {
      init.value = false;
    });
    return {
      hasData,
      ability,
      init,
      getParams,
      dataSource,
      loading,
      updateData,
      fetchData,
      gotoDetail,
    };
  },
  render() {
    return (
      <HeroicLayout align={this.hasData ? undefined : 'center'}>
        <div slot="hero">
          <header class={styles.hero}>
            <div class={styles.title}>受益所有人批量识别</div>
            <div class={styles.batch}>
              <BatchUpload
                action={(file) =>
                  `/rover/batch/import/diligence/excel?${qs.stringify({
                    fileName: file.name,
                  })}`
                }
                // beforeFileUpload={() => this.checkUsage()}
                // 多种新建任务的最后都会去出触发这个事件
                onUpdateData={this.updateData}
              />
            </div>
          </header>
        </div>
        {/* 新手引导 */}
        <Spin spinning={this.init}>
          <GuideWidget v-show={!this.hasData} />

          {/* 因为需要调用search，用v-show */}
          <SearchResult
            v-show={this.hasData}
            loading={this.loading}
            rowKey={'batchId'}
            scroll={{ x: true }}
            dataSource={this.dataSource}
            columns={TABLE_COLUMNS}
            pagination={{}}
            onAction={this.gotoDetail}
            onExport={() => this.$track(createTrackEvent(6208, '批量排查', '导出名单'))}
          ></SearchResult>
        </Spin>
      </HeroicLayout>
    );
  },
});

export default IdentifyBatchPage;
