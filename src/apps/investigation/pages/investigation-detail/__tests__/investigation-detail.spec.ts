import { shallowMount } from '@vue/test-utils';

import { company as companyService, diligence } from '@/shared/services';

import InvestigateDetailPage from '..';

const mockRoute = {
  query: {
    from: 'investigation',
    diligenceId: '123',
    orgModelIds: '1,2,3',
  },
  params: {
    id: '456',
  },
  path: '/investigation/detail',
};

const mockDetailData = {
  isFirstToOrg: 0,
  isFirstToModel: 0,
  paid: 0,
  notMatch: false,
  id: 50996905,
  orgId: 208,
  companyId: '84c17a005a759a5e0d875c1ebb6c9846',
  name: '乐视网信息技术（北京）股份有限公司',
  score: 65,
  result: 1,
  operator: 101618,
  orgModelId: 3487,
  modelBranchCode: 'f0a79aeb-c7a3-42fc-875b-d6406a7305be',
  product: 'SAAS_UBO',
  details: {
    result: 1,
    modelType: 1,
    totalHits: 407,
    levelGroup: {
      '0': [],
      '1': [
        {
          level: 1,
          totalHits: 84,
          scoreDetails: [
            {
              name: '股权冻结',
              score: 8,
              isVeto: 0,
              status: 2,
              metricsId: 141544,
              riskLevel: 1,
              totalHits: 82,
              hitDetails: {
                must: [
                  {
                    source: 'CreditES',
                    status: 2,
                    totalHits: 82,
                    strategyId: 225012,
                    description:
                      '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
                    dimensionKey: 'FreezeEquity',
                    strategyName: '股权冻结',
                    strategyRole: 1,
                    dimensionName: '股权冻结',
                  },
                ],
                totalHits: 82,
                hitStrategy: {
                  must: [225012],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 8,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              },
              metricType: 0,
              detailsJson: {},
              hitStrategy: [
                {
                  must: [225012],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 8,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              ],
              otherHitDetails: [],
            },
            {
              name: '被列入经营异常名录（历史）',
              score: 2,
              isVeto: 0,
              status: 2,
              metricsId: 141542,
              riskLevel: 1,
              totalHits: 1,
              hitDetails: {
                must: [
                  {
                    source: 'CreditES',
                    status: 2,
                    totalHits: 1,
                    strategyId: 225023,
                    description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
                    dimensionKey: 'OperationAbnormal',
                    strategyName: '被列入经营异常名录（历史）',
                    strategyRole: 1,
                    dimensionName: '被列入经营异常名录（历史）',
                  },
                ],
                totalHits: 1,
                hitStrategy: {
                  must: [225023],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 2,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              },
              metricType: 0,
              detailsJson: {},
              hitStrategy: [
                {
                  must: [225023],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 2,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              ],
              otherHitDetails: [],
            },
            {
              name: '企查分',
              score: 5,
              isVeto: 0,
              status: 2,
              metricsId: 141535,
              riskLevel: 1,
              totalHits: 1,
              hitDetails: {
                must: [
                  {
                    source: 'EnterpriseLib',
                    status: 2,
                    totalHits: 1,
                    strategyId: 225022,
                    description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
                    dimensionKey: 'QCCCreditRate',
                    strategyName: '企查分',
                    strategyRole: 1,
                    dimensionName: '企查分',
                  },
                ],
                totalHits: 1,
                hitStrategy: {
                  must: [225022],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 5,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              },
              metricType: 0,
              detailsJson: {
                scoreStrategy: 0,
              },
              hitStrategy: [
                {
                  must: [225022],
                  order: 0,
                  status: 1,
                  scoreSettings: {
                    maxScore: 5,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              ],
              otherHitDetails: [],
            },
          ],
          groupDefinition: {
            comment: '经营风险',
            groupId: 50013828,
            groupName: '经营风险',
            isVirtual: null,
            riskLevel: 2,
            detailsJson: null,
            parentGroupId: null,
          },
        },
      ],
      '2': [
        {
          level: 2,
          totalHits: 323,
          scoreDetails: [
            {
              name: '法定代表人限制高消费',
              score: 50,
              isVeto: 0,
              status: 2,
              metricsId: 141545,
              riskLevel: 2,
              totalHits: 323,
              hitDetails: {
                should: [
                  {
                    source: 'CreditES',
                    status: 2,
                    totalHits: 323,
                    strategyId: 225021,
                    description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
                    dimensionKey: 'RestrictedConsumptionCurrent',
                    strategyName: '限制高消费',
                    strategyRole: 1,
                    dimensionName: '被列入限制高消费名单',
                  },
                ],
                totalHits: 323,
                hitStrategy: {
                  order: 0,
                  should: [225021],
                  status: 1,
                  scoreSettings: {
                    maxScore: 50,
                    riskLevel: 2,
                  },
                  minimum_should_match: 1,
                },
                minimum_should_match: 1,
              },
              metricType: 0,
              detailsJson: {},
              hitStrategy: [
                {
                  order: 0,
                  should: [225021],
                  status: 1,
                  scoreSettings: {
                    maxScore: 50,
                    riskLevel: 2,
                  },
                  minimum_should_match: 1,
                },
                {
                  order: 3,
                  should: [225026],
                  status: 1,
                  scoreSettings: {
                    maxScore: 5,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
                {
                  order: 2,
                  should: [225027],
                  status: 1,
                  scoreSettings: {
                    maxScore: 15,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
                {
                  order: 1,
                  should: [225028],
                  status: 1,
                  scoreSettings: {
                    maxScore: 10,
                    riskLevel: 1,
                  },
                  minimum_should_match: 1,
                },
              ],
              otherHitDetails: [],
            },
          ],
          groupDefinition: {
            comment: '经营风险',
            groupId: 50013828,
            groupName: '经营风险',
            isVirtual: null,
            riskLevel: 2,
            detailsJson: null,
            parentGroupId: null,
          },
        },
      ],
    },
    totalScore: 65,
    vetoMetrics: [],
    originalHits: [
      {
        name: '股权冻结',
        score: 8,
        isVeto: 0,
        status: 2,
        metricsId: 141544,
        riskLevel: 1,
        totalHits: 82,
        hitDetails: {
          must: [
            {
              source: 'CreditES',
              status: 2,
              totalHits: 82,
              strategyId: 225012,
              description:
                '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
              dimensionKey: 'FreezeEquity',
              strategyName: '股权冻结',
              strategyRole: 1,
              dimensionName: '股权冻结',
            },
          ],
          totalHits: 82,
          hitStrategy: {
            must: [225012],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 8,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        },
        metricType: 0,
        detailsJson: {},
        hitStrategy: [
          {
            must: [225012],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 8,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        ],
        otherHitDetails: [],
      },
      {
        name: '法定代表人限制高消费',
        score: 50,
        isVeto: 0,
        status: 2,
        metricsId: 141545,
        riskLevel: 2,
        totalHits: 323,
        hitDetails: {
          should: [
            {
              source: 'CreditES',
              status: 2,
              totalHits: 323,
              strategyId: 225021,
              description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
              dimensionKey: 'RestrictedConsumptionCurrent',
              strategyName: '限制高消费',
              strategyRole: 1,
              dimensionName: '被列入限制高消费名单',
            },
          ],
          totalHits: 323,
          hitStrategy: {
            order: 0,
            should: [225021],
            status: 1,
            scoreSettings: {
              maxScore: 50,
              riskLevel: 2,
            },
            minimum_should_match: 1,
          },
          minimum_should_match: 1,
        },
        metricType: 0,
        detailsJson: {},
        hitStrategy: [
          {
            order: 0,
            should: [225021],
            status: 1,
            scoreSettings: {
              maxScore: 50,
              riskLevel: 2,
            },
            minimum_should_match: 1,
          },
          {
            order: 3,
            should: [225026],
            status: 1,
            scoreSettings: {
              maxScore: 5,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
          {
            order: 2,
            should: [225027],
            status: 1,
            scoreSettings: {
              maxScore: 15,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
          {
            order: 1,
            should: [225028],
            status: 1,
            scoreSettings: {
              maxScore: 10,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        ],
        otherHitDetails: [],
      },
      {
        name: '被列入经营异常名录（历史）',
        score: 2,
        isVeto: 0,
        status: 2,
        metricsId: 141542,
        riskLevel: 1,
        totalHits: 1,
        hitDetails: {
          must: [
            {
              source: 'CreditES',
              status: 2,
              totalHits: 1,
              strategyId: 225023,
              description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
              dimensionKey: 'OperationAbnormal',
              strategyName: '被列入经营异常名录（历史）',
              strategyRole: 1,
              dimensionName: '被列入经营异常名录（历史）',
            },
          ],
          totalHits: 1,
          hitStrategy: {
            must: [225023],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 2,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        },
        metricType: 0,
        detailsJson: {},
        hitStrategy: [
          {
            must: [225023],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 2,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        ],
        otherHitDetails: [],
      },
      {
        name: '企查分',
        score: 5,
        isVeto: 0,
        status: 2,
        metricsId: 141535,
        riskLevel: 1,
        totalHits: 1,
        hitDetails: {
          must: [
            {
              source: 'EnterpriseLib',
              status: 2,
              totalHits: 1,
              strategyId: 225022,
              description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
              dimensionKey: 'QCCCreditRate',
              strategyName: '企查分',
              strategyRole: 1,
              dimensionName: '企查分',
            },
          ],
          totalHits: 1,
          hitStrategy: {
            must: [225022],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 5,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        },
        metricType: 0,
        detailsJson: {
          scoreStrategy: 0,
        },
        hitStrategy: [
          {
            must: [225022],
            order: 0,
            status: 1,
            scoreSettings: {
              maxScore: 5,
              riskLevel: 1,
            },
            minimum_should_match: 1,
          },
        ],
        otherHitDetails: [],
      },
    ],
    dimensionHits: ['FreezeEquity', 'RestrictedConsumptionCurrent', 'OperationAbnormal', 'RestrictedConsumptionHistory', 'QCCCreditRate'],
    groupMetricScores: [
      {
        level: 2,
        score: 65,
        totalHits: 407,
        scoreDetails: [
          {
            name: '股权冻结',
            score: 8,
            isVeto: 0,
            status: 2,
            metricsId: 141544,
            riskLevel: 1,
            totalHits: 82,
            hitDetails: {
              must: [
                {
                  source: 'CreditES',
                  status: 2,
                  totalHits: 82,
                  strategyId: 225012,
                  description:
                    '匹配到目标主体 <em class=" - ">【股权冻结】 82条记录</em><span class="">，冻结股权数额：<em class=" - ">699,893.48万元</em></span>',
                  dimensionKey: 'FreezeEquity',
                  strategyName: '股权冻结',
                  strategyRole: 1,
                  dimensionName: '股权冻结',
                },
              ],
              totalHits: 82,
              hitStrategy: {
                must: [225012],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 8,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            },
            metricType: 0,
            detailsJson: {},
            hitStrategy: [
              {
                must: [225012],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 8,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            otherHitDetails: [],
          },
          {
            name: '法定代表人限制高消费',
            score: 50,
            isVeto: 0,
            status: 2,
            metricsId: 141545,
            riskLevel: 2,
            totalHits: 323,
            hitDetails: {
              should: [
                {
                  source: 'CreditES',
                  status: 2,
                  totalHits: 323,
                  strategyId: 225021,
                  description: '匹配到目标主体 <em class=" - "></em><em class=" - ">【限制高消费】 323条记录</em>',
                  dimensionKey: 'RestrictedConsumptionCurrent',
                  strategyName: '限制高消费',
                  strategyRole: 1,
                  dimensionName: '被列入限制高消费名单',
                },
              ],
              totalHits: 323,
              hitStrategy: {
                order: 0,
                should: [225021],
                status: 1,
                scoreSettings: {
                  maxScore: 50,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
              minimum_should_match: 1,
            },
            metricType: 0,
            detailsJson: {},
            hitStrategy: [
              {
                order: 0,
                should: [225021],
                status: 1,
                scoreSettings: {
                  maxScore: 50,
                  riskLevel: 2,
                },
                minimum_should_match: 1,
              },
              {
                order: 3,
                should: [225026],
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
              {
                order: 2,
                should: [225027],
                status: 1,
                scoreSettings: {
                  maxScore: 15,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
              {
                order: 1,
                should: [225028],
                status: 1,
                scoreSettings: {
                  maxScore: 10,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            otherHitDetails: [],
          },
          {
            name: '被列入经营异常名录（历史）',
            score: 2,
            isVeto: 0,
            status: 2,
            metricsId: 141542,
            riskLevel: 1,
            totalHits: 1,
            hitDetails: {
              must: [
                {
                  source: 'CreditES',
                  status: 2,
                  totalHits: 1,
                  strategyId: 225023,
                  description: '匹配到目标主体 <em class=" - ">【被列入经营异常名录（历史）】 1条记录</em>',
                  dimensionKey: 'OperationAbnormal',
                  strategyName: '被列入经营异常名录（历史）',
                  strategyRole: 1,
                  dimensionName: '被列入经营异常名录（历史）',
                },
              ],
              totalHits: 1,
              hitStrategy: {
                must: [225023],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 2,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            },
            metricType: 0,
            detailsJson: {},
            hitStrategy: [
              {
                must: [225023],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 2,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            otherHitDetails: [],
          },
          {
            name: '企查分',
            score: 5,
            isVeto: 0,
            status: 2,
            metricsId: 141535,
            riskLevel: 1,
            totalHits: 1,
            hitDetails: {
              must: [
                {
                  source: 'EnterpriseLib',
                  status: 2,
                  totalHits: 1,
                  strategyId: 225022,
                  description: '匹配到目标主体 <em class=" - ">【企查分】 1分</em>',
                  dimensionKey: 'QCCCreditRate',
                  strategyName: '企查分',
                  strategyRole: 1,
                  dimensionName: '企查分',
                },
              ],
              totalHits: 1,
              hitStrategy: {
                must: [225022],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            },
            metricType: 0,
            detailsJson: {
              scoreStrategy: 0,
            },
            hitStrategy: [
              {
                must: [225022],
                order: 0,
                status: 1,
                scoreSettings: {
                  maxScore: 5,
                  riskLevel: 1,
                },
                minimum_should_match: 1,
              },
            ],
            otherHitDetails: [],
          },
        ],
        groupDefinition: {
          comment: '经营风险',
          groupId: 50013828,
          groupName: '经营风险',
          isVirtual: null,
          riskLevel: 2,
          detailsJson: null,
          parentGroupId: null,
        },
      },
    ],
  },
  shouldUpdate: 0,
  updateDate: '2025-03-25T02:46:22.000Z',
  snapshotDate: '2025-03-25T02:46:16.000Z',
  snapshotId: '527f38a0-0923-11f0-915b-d1c3fe829a10',
  createDate: '2025-03-25T02:46:16.000Z',
  creditRate: null,
  type: 0,
  cached: true,
};

const mockCompanyInfo = {
  KeyNo: '84c17a005a759a5e0d875c1ebb6c9846',
  Name: '乐视网信息技术（北京）股份有限公司',
  Oper: {
    Org: 2,
    KeyNo: 'pr1b0263756a68c7cd303c575c7fe796',
    Name: '刘延峰',
    HasImage: true,
    CompanyCount: 21,
    OperType: 1,
    ImageUrl: 'https://image.qcc.com/person/pr1b0263756a68c7cd303c575c7fe796.jpg?x-oss-process=style/person_120',
    IsAC: 1,
  },
  TagsInfoV2: [
    {
      Type: 905,
      Name: '发票抬头',
      ShortName: null,
      DataExtend: '',
      TradingPlaceCode: null,
      TradingPlaceName: null,
    },
    {
      Type: 903,
      Name: '存续',
      ShortName: null,
      DataExtend: '',
      TradingPlaceCode: null,
      TradingPlaceName: null,
    },
    {
      Type: 907,
      Name: '债券违约',
      ShortName: '',
      DataExtend: '',
      TradingPlaceCode: null,
      TradingPlaceName: null,
    },
  ],
};

vi.mock('@/shared/services');
vi.mock('vue-router/composables', () => ({
  useRoute: () => mockRoute,
  useRouter: () => ({
    replace: vi.fn(),
    push: vi.fn(),
    back: vi.fn(),
  }),
}));

vi.mock('@/components/full-watermark', () => ({
  default: {
    functional: true,
    render(h, context) {
      return h(
        'q-icon-stub',
        {
          attrs: context.props,
        },
        context.children
      );
    },
  },
}));

describe('InvestigateDetailPage', () => {
  beforeEach(() => {
    vi.mocked(diligence.scanRiskDetail).mockResolvedValue(mockDetailData);
    vi.mocked(companyService.getDetail).mockResolvedValue(mockCompanyInfo);
    vi.useFakeTimers({
      now: new Date('2024-01-01T00:00:00.000Z'),
    });
  });
  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  test('正确渲染', async () => {
    const wrapper = shallowMount(InvestigateDetailPage, {
      propsData: {
        isExternal: false,
      },
    });

    setTimeout(() => {
      expect(wrapper.vm.loading).toBeFalsy();
      expect(wrapper.html()).toMatchSnapshot();
    }, 50);
  });

  test('外部嵌入页面不展示面包屑', () => {
    const wrapper = shallowMount(InvestigateDetailPage, {
      propsData: {
        isExternal: true,
      },
    });
    const breadcrumb = wrapper.findComponent({ name: 'ABreadcrumb' });
    expect(breadcrumb.exists()).toBe(false);
  });
});
