import { defineComponent, ref, computed, onMounted, PropType } from 'vue';
// import dataLoader from '../../data';
import AppPath from '../path';
import AppTag from '../tag';
import styles from './person.module.less';
import CompanyLogo from '@/components/company-logo';
import { formatPersonOverview } from './utils';
import testdata from './mock.json';
import QEntityLink from '@/components/global/q-entity-link';

interface PersonData {
  name: string;
  image: string;
  tags: Array<{
    name: string;
    type: string;
  }>;
  ename: string;
  eid: string;
  jobTitle: string;
  summary: Array<{
    label: string;
    count?: number;
  }>;
}

// TODO mock
const loadPersonDetail = (...rest) => {
  return Promise.resolve(formatPersonOverview(testdata.result));
};

const AppPerson = defineComponent({
  name: 'app-person',
  props: {
    id: {
      type: String,
      default: '',
    },
    pathData: {
      type: Object as PropType<{
        Path: any[];
        category: string;
      }>,
      default: undefined,
    },
    hidePersonCompanyFlag: {
      type: Boolean,
      default: false,
    },
    isForbidOurterLinkerFlag: {
      type: Boolean,
      default: false,
    },
    getCompanyOrPersonLinkerByOrg: {
      type: Function as PropType<(name: string, id: string, org?: any, options?: any) => string>,
      default: undefined,
    },
    glf: {
      type: Object,
      default: undefined,
    },
  },
  setup(props) {
    const isLoaded = ref(false);
    const name = ref('');
    const image = ref('');
    const tags = ref<Array<{ name: string; type: string }>>([]);
    const ename = ref('');
    const eid = ref('');
    const jobTitle = ref('');
    const summary = ref<Array<{ label: string; count?: number }>>([]);
    const path = ref<any[]>([]);
    const category = ref('');

    onMounted(() => {
      if (props.id) {
        loadPersonDetail(eid.value, props.id)
          .then((data) => {
            isLoaded.value = true;
            name.value = data.name;
            image.value = data.image;
            tags.value = data.tags;
            ename.value = data.ename;
            eid.value = data.eid;
            jobTitle.value = data.jobTitle;
            summary.value = data.summary;
            if (props.pathData) {
              path.value = props.pathData.Path;
              category.value = props.pathData.category;
            }
          })
          .catch(() => {});
      } else {
        isLoaded.value = true;
      }
    });

    return {
      isLoaded,
      name,
      image,
      tags,
      ename,
      eid,
      jobTitle,
      summary,
      path,
      category,
    };
  },

  render() {
    return (
      <div class={styles.personContainer}>
        <transition name="fade">
          {this.isLoaded && (
            <div onClick={(e: Event) => e.stopPropagation()}>
              <CompanyLogo class={styles.avatar} size="50px" src={this.image} id={this.id} name={this.name} hasimage={this.image ? 1 : 0} />
              <div class={styles.contentContainer}>
                <QEntityLink coyObj={{ KeyNo: this.id, Name: this.name }} />
                {!this.isForbidOurterLinkerFlag && (
                  <div class={[styles.rowItem, styles.marginTop3]}>
                    {this.tags.map((tag) => (
                      <AppTag key={tag.name} type={tag.type} text={tag.name} title="" />
                    ))}
                  </div>
                )}
                {!this.hidePersonCompanyFlag && (
                  <div class={styles.rowItem}>
                    <span class={styles.labelText}>企业：</span>
                    <span class={styles.content}>
                      <QEntityLink coyObj={{ KeyNo: this.eid, Name: this.ename }} />
                    </span>
                  </div>
                )}
                {this.jobTitle && (
                  <div class={styles.rowItem}>
                    <span class={styles.labelText}>职位：</span>
                    <span class={styles.content}>{this.jobTitle || '-'}</span>
                  </div>
                )}
              </div>
              <div style={{ clear: 'both' }} />
              <div class={styles.gap} />
              <div class={styles.summaryContainer}>
                {this.summary.map((item, index) => (
                  <div key={index} class={[styles.rowItem, styles.colItem]}>
                    <span class={styles.labelText}>{item.label}：</span>
                    {item.count ? <span class={styles.content}>{item.count}家</span> : <span class={styles.content}>-</span>}
                  </div>
                ))}
              </div>
              <div style={{ clear: 'both' }} />
              {this.path && this.path.length > 0 && <div class={styles.gap} />}
              {this.path && this.path.length > 0 && (
                <AppPath paths={this.path} category={this.category} keyNo={this.id} glf2={!!this.glf} />
              )}
            </div>
          )}
        </transition>
        {!this.isLoaded && <div class={styles.loading} />}
      </div>
    );
  },
});

export default AppPerson;
