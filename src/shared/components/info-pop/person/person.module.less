@gutter: 15px;

.person-container {
  font-family: "Microsoft YaHei", Arial, sans-serif;
  width: 340px;
  padding: @gutter;
  box-shadow: rgba(0, 0, 0, 0.2) 0 2px 4px 0;
  background-color: #fff;
  border-radius: 6px;

  .loading {
    width: 340px;
    height: 120px;
    background: url('./images/loading.png') center center;
    background-size: cover;
    margin: 0 0 0 -@gutter;

  }

  .avatar {
    width: 50px;
    height: 50px;
    float: left;
  }

  a {
    color: #128BED;
    text-decoration: none;

    &:hover {
      color: #3071a9;
    }
  }

  // :global {
  //   .peron-link {
  //     font-size: 14px !important;
  //     word-break: break-all;

  //     &.full-height{
  //       line-height: 50px;
  //     }
  //   }
  // }

  .content-container {
    width: 260px;
  }

  .content-container,
  .summary-container {
    float: left;
    padding-left: @gutter;

    .row-item {
      margin-top: 7px;

      &.margin-top-3 {
        margin-top: 3px;
      }
    }

    .label-text {
      color: #999;
      font-size: 14px;
    }

    .content {
      color: #333;
      font-size: 14px;
    }
  }

  .gap {
    margin: @gutter / 2 + 4 -@gutter @gutter / 2 -@gutter;
    height: 5px;
    background: rgba(246, 246, 246, 1);
  }

  .summary-container {
    float: unset;
    padding-left: 0;

    .col-item {
      width: 50%;
      display: inline-block;
    }
  }
}
