import Vue, { Ref, ref } from 'vue';
import { Point } from 'yfiles';

const getPosition = (trigger, popover, container) => {
  const triggerRect = trigger.getBoundingClientRect();
  const popoverRect = popover.getBoundingClientRect();
  const containerRect = container.getBoundingClientRect();

  const spaceRight = containerRect.right - triggerRect.right;
  const spaceBottom = containerRect.bottom - triggerRect.bottom;
  const spaceLeft = triggerRect.left - containerRect.left;
  const spaceTop = triggerRect.top - containerRect.top;

  const position = { top: triggerRect.bottom, left: triggerRect.left };

  if (spaceRight < popoverRect.width && spaceLeft >= popoverRect.width) {
    position.left = triggerRect.left - popoverRect.width;
  }

  if (spaceBottom < popoverRect.height && spaceTop >= popoverRect.height) {
    position.top = triggerRect.top - popoverRect.height;
  }

  popover.style.top = `${position.top - containerRect.top}px`;
  popover.style.left = `${position.left - containerRect.left}px`;

  return position;
};

export const usePopover = (parentDom: Ref<HTMLElement | null>) => {
  const timer = ref<number>();
  const container = ref<HTMLElement>();

  const createContainer = (x = 0, y = 0) => {
    const div = document.createElement('div');
    div.style.position = 'absolute';
    div.style.top = `${y}px`;
    div.style.left = `${x}px`;
    div.style.transition = 'all 1s ease';
    container.value = div;
  };

  const move = ({ nodeItem, graphComponent }) => {
    if (!container.value) return;
    const { x, y, width, height, topLeft } = nodeItem.layout;
    const viewPoint = graphComponent.toViewCoordinates(new Point(topLeft.x, topLeft.y));
    const gap = 5;
    // const leftPosition = {
    //   x: viewPoint.x - gap,
    //   y: viewPoint.y,
    //   isLeft: true,
    // };
    // const rightPosition = {
    //   x: viewPoint.x + width + gap,
    //   y: viewPoint.y,
    //   isLeft: false,
    // };
    const position = {
      x: viewPoint.x + width + gap,
      y: viewPoint.y,
      isLeft: false,
    };
    container.value.style.top = `${position.y}px`;
    container.value.style.left = `${position.x}px`;
  };

  const createContent = (comp, configs: Record<string, any> = {}) => {
    if (!container.value || !parentDom.value) return;
    const Ctor: any = Vue.extend(comp);
    const instance = new Ctor({
      propsData: configs.props,
      data() {
        return configs.data;
      },
    });
    instance.$mount();
    container.value.appendChild(instance.$el);
    parentDom.value.appendChild(container.value);
  };

  const unload = () => {
    timer.value = setTimeout(() => {
      if (!container.value || !parentDom.value) {
        timer.value && clearTimeout(timer.value);
        return;
      }
      if (parentDom.value?.contains(container.value)) {
        parentDom.value.removeChild(container.value);
      }
    }, 300);
  };

  const load = (comp, configs: Record<string, any> = {}) => {
    createContainer();
    createContent(comp, configs);
    container.value?.addEventListener('mouseover', () => {
      timer.value && clearTimeout(timer.value);
    });
    container.value?.addEventListener('mouseout', () => {
      unload();
    });
  };

  return {
    load,
    unload,
    move,
  };
};
